import Image from 'next/image';
import Link from 'next/link';
import { Calendar, Clock, User, ArrowLeft, Tag } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import type { BlogFrontmatter } from '@/types/blog';

interface BlogHeaderProps {
  frontmatter: BlogFrontmatter;
  locale: string;
  readingTime?: number;
  backUrl?: string;
}

export function BlogHeader({ 
  frontmatter, 
  locale, 
  readingTime,
  backUrl 
}: BlogHeaderProps) {
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString(locale === 'zh' ? 'zh-CN' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getHeaderStyle = () => {
    const style = frontmatter.style?.headerStyle || 'clean';
    
    switch (style) {
      case 'gradient':
        return 'bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10';
      case 'minimal':
        return 'bg-transparent';
      case 'image':
        return frontmatter.coverImage 
          ? 'relative bg-black text-white' 
          : 'bg-muted/50';
      default:
        return 'bg-muted/30';
    }
  };

  const showAuthor = frontmatter.style?.showAuthor !== false;
  const showDate = frontmatter.style?.showDate !== false;
  const showTags = frontmatter.style?.showTags !== false;
  const showCategory = frontmatter.style?.showCategory !== false;

  return (
    <header className={`relative overflow-hidden ${getHeaderStyle()}`}>
      {/* 背景图片 */}
      {frontmatter.style?.headerStyle === 'image' && frontmatter.coverImage && (
        <>
          <div className="absolute inset-0">
            <Image
              src={frontmatter.coverImage}
              alt={frontmatter.title}
              fill
              className="object-cover"
              priority
            />
          </div>
          <div className="absolute inset-0 bg-black/50" />
        </>
      )}
      
      <div className="relative z-10 max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* 返回按钮 */}
        {backUrl && (
          <div className="mb-6">
            <Link href={backUrl}>
              <Button variant="ghost" size="sm" className="gap-2">
                <ArrowLeft className="w-4 h-4" />
                {locale === 'zh' ? '返回博客' : 'Back to Blog'}
              </Button>
            </Link>
          </div>
        )}

        {/* 分类和标签 */}
        <div className="flex flex-wrap items-center gap-3 mb-4">
          {showCategory && (
            <Badge variant="secondary" className="text-sm">
              {frontmatter.category}
            </Badge>
          )}
          {frontmatter.featured && (
            <Badge variant="outline" className="text-sm">
              ⭐ {locale === 'zh' ? '精选' : 'Featured'}
            </Badge>
          )}
          {showTags && frontmatter.tags.length > 0 && (
            <div className="flex items-center gap-2">
              <Tag className="w-4 h-4 text-muted-foreground" />
              <div className="flex flex-wrap gap-1">
                {frontmatter.tags.slice(0, 5).map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {frontmatter.tags.length > 5 && (
                  <Badge variant="outline" className="text-xs">
                    +{frontmatter.tags.length - 5}
                  </Badge>
                )}
              </div>
            </div>
          )}
        </div>

        {/* 标题 */}
        <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight mb-4">
          {frontmatter.title}
        </h1>

        {/* 描述 */}
        {frontmatter.description && (
          <p className="text-lg sm:text-xl text-muted-foreground mb-6 max-w-3xl">
            {frontmatter.description}
          </p>
        )}

        {/* 元信息 */}
        <div className="flex flex-wrap items-center gap-6 text-sm text-muted-foreground">
          {showAuthor && (
            <div className="flex items-center gap-2">
              <User className="w-4 h-4" />
              <span>{frontmatter.author}</span>
            </div>
          )}
          
          {showDate && (
            <div className="flex items-center gap-2">
              <Calendar className="w-4 h-4" />
              <span>{formatDate(frontmatter.date)}</span>
            </div>
          )}
          
          {readingTime && (
            <div className="flex items-center gap-2">
              <Clock className="w-4 h-4" />
              <span>
                {Math.ceil(readingTime)} {locale === 'zh' ? '分钟阅读' : 'min read'}
              </span>
            </div>
          )}
        </div>

        {/* 封面图片（非背景模式） */}
        {frontmatter.coverImage && frontmatter.style?.headerStyle !== 'image' && (
          <div className="mt-8 rounded-lg overflow-hidden">
            <Image
              src={frontmatter.coverImage}
              alt={frontmatter.title}
              width={800}
              height={400}
              className="w-full h-auto"
              priority
            />
          </div>
        )}
      </div>
      
      {/* 分隔线 */}
      <Separator className="mt-0" />
    </header>
  );
}
