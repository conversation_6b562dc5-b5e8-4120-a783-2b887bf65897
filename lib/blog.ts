import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import readingTime from 'reading-time';
import MarkdownIt from 'markdown-it';
import type {
  BlogPost,
  BlogFrontmatter,
  BlogListItem,
  BlogCategories
} from '@/types/blog';

const md = new MarkdownIt({
  html: true,
  linkify: true,
  typographer: true,
});

const BLOG_CONTENT_PATH = path.join(process.cwd(), 'content/blog');

/**
 * 获取指定语言的博客目录路径
 */
function getBlogPath(locale: string): string {
  return path.join(BLOG_CONTENT_PATH, locale);
}

/**
 * 递归获取目录下所有 MDX 文件
 */
function getAllMdxFiles(dir: string): string[] {
  const files: string[] = [];
  
  if (!fs.existsSync(dir)) {
    return files;
  }
  
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      files.push(...getAllMdxFiles(fullPath));
    } else if (item.endsWith('.mdx')) {
      files.push(fullPath);
    }
  }
  
  return files;
}

/**
 * 从文件路径生成 slug
 */
function generateSlugFromPath(filePath: string, locale: string): string {
  const blogPath = getBlogPath(locale);
  const relativePath = path.relative(blogPath, filePath);
  const { name } = path.parse(relativePath);
  return name;
}

/**
 * 生成文章摘要
 */
function generateExcerpt(content: string, maxLength: number = 160): string {
  // 移除 MDX 语法和 HTML 标签
  const plainText = content
    .replace(/^---[\s\S]*?---/, '') // 移除 frontmatter
    .replace(/import\s+.*?from\s+['"].*?['"];?\s*/g, '') // 移除 import 语句
    .replace(/<[^>]*>/g, '') // 移除 HTML 标签
    .replace(/!\[.*?\]\(.*?\)/g, '') // 移除图片
    .replace(/\[.*?\]\(.*?\)/g, '') // 移除链接
    .replace(/`.*?`/g, '') // 移除行内代码
    .replace(/```[\s\S]*?```/g, '') // 移除代码块
    .replace(/#{1,6}\s+/g, '') // 移除标题标记
    .replace(/\n+/g, ' ') // 替换换行为空格
    .trim();
  
  if (plainText.length <= maxLength) {
    return plainText;
  }
  
  return plainText.substring(0, maxLength).replace(/\s+\S*$/, '') + '...';
}

/**
 * 解析单个博客文件
 */
export async function parseBlogPost(filePath: string, locale: string): Promise<BlogPost | null> {
  try {
    const fileContent = fs.readFileSync(filePath, 'utf8');
    const { data, content } = matter(fileContent);

    const frontmatter = data as BlogFrontmatter;

    // 检查是否已发布
    if (frontmatter.published === false) {
      return null;
    }

    const slug = frontmatter.slug || generateSlugFromPath(filePath, locale);
    const excerpt = generateExcerpt(content);
    const readingTimeResult = readingTime(content);

    // 清理 MDX 内容，移除 import 语句和其他 MDX 特定语法
    const cleanContent = content
      .replace(/^import\s+.*?from\s+['"].*?['"];?\s*$/gm, '') // 移除 import 语句
      .replace(/<([A-Z][A-Za-z0-9]*)[^>]*>[\s\S]*?<\/\1>/g, '') // 移除自定义组件
      .replace(/<([A-Z][A-Za-z0-9]*)[^>]*\/>/g, '') // 移除自闭合自定义组件
      .trim();

    // 简单的 Markdown 到 HTML 转换（避免 markdown-it 的问题）
    let htmlContent = cleanContent
      .replace(/^# (.*$)/gim, '<h1>$1</h1>')
      .replace(/^## (.*$)/gim, '<h2>$1</h2>')
      .replace(/^### (.*$)/gim, '<h3>$1</h3>')
      .replace(/^#### (.*$)/gim, '<h4>$1</h4>')
      .replace(/\*\*(.*?)\*\*/gim, '<strong>$1</strong>')
      .replace(/\*(.*?)\*/gim, '<em>$1</em>')
      .replace(/`(.*?)`/gim, '<code>$1</code>')
      .replace(/\[([^\]]+)\]\(([^)]+)\)/gim, '<a href="$2">$1</a>')
      .replace(/\n\n/gim, '</p><p>')
      .replace(/\n/gim, '<br>');

    // 包装在段落标签中
    if (htmlContent && !htmlContent.startsWith('<')) {
      htmlContent = '<p>' + htmlContent + '</p>';
    }

    return {
      slug,
      frontmatter,
      content: htmlContent,
      excerpt,
      readingTime: readingTimeResult.minutes,
      locale,
    };
  } catch (error) {
    console.error(`Error parsing blog post ${filePath}:`, error);
    return null;
  }
}

/**
 * 获取所有博客文章
 */
export async function getAllBlogPosts(locale: string): Promise<BlogPost[]> {
  const blogPath = getBlogPath(locale);
  const mdxFiles = getAllMdxFiles(blogPath);
  
  const posts: BlogPost[] = [];
  
  for (const filePath of mdxFiles) {
    const post = await parseBlogPost(filePath, locale);
    if (post) {
      posts.push(post);
    }
  }
  
  // 按日期排序（最新的在前）
  return posts.sort((a, b) => 
    new Date(b.frontmatter.date).getTime() - new Date(a.frontmatter.date).getTime()
  );
}

/**
 * 获取博客文章列表（用于列表页）
 */
export async function getBlogList(locale: string): Promise<BlogListItem[]> {
  const posts = await getAllBlogPosts(locale);
  
  return posts.map(post => ({
    slug: post.slug,
    title: post.frontmatter.title,
    description: post.frontmatter.description,
    date: post.frontmatter.date,
    author: post.frontmatter.author,
    category: post.frontmatter.category,
    tags: post.frontmatter.tags,
    coverImage: post.frontmatter.coverImage,
    excerpt: post.excerpt,
    readingTime: post.readingTime,
    featured: post.frontmatter.featured,
  }));
}

/**
 * 根据 slug 获取单个博客文章
 */
export async function getBlogPost(slug: string, locale: string): Promise<BlogPost | null> {
  const posts = await getAllBlogPosts(locale);
  return posts.find(post => post.slug === slug) || null;
}

/**
 * 获取博客分类配置
 */
export async function getBlogCategories(locale: string): Promise<BlogCategories> {
  try {
    const categoriesPath = path.join(getBlogPath(locale), 'categories.json');
    const categoriesContent = fs.readFileSync(categoriesPath, 'utf8');
    return JSON.parse(categoriesContent);
  } catch (error) {
    console.error(`Error loading categories for locale ${locale}:`, error);
    return {};
  }
}

/**
 * 根据分类获取博客文章
 */
export async function getBlogPostsByCategory(category: string, locale: string): Promise<BlogListItem[]> {
  const posts = await getBlogList(locale);
  return posts.filter(post => post.category === category);
}

/**
 * 根据标签获取博客文章
 */
export async function getBlogPostsByTag(tag: string, locale: string): Promise<BlogListItem[]> {
  const posts = await getBlogList(locale);
  return posts.filter(post => post.tags.includes(tag));
}

/**
 * 获取所有可用的 slug（用于静态生成）
 */
export async function getAllBlogSlugs(): Promise<Array<{ locale: string; slug: string }>> {
  const locales = ['en', 'zh']; // 根据你的项目配置调整
  const slugs: Array<{ locale: string; slug: string }> = [];
  
  for (const locale of locales) {
    const posts = await getAllBlogPosts(locale);
    for (const post of posts) {
      slugs.push({ locale, slug: post.slug });
    }
  }
  
  return slugs;
}

/**
 * 获取相关文章
 */
export async function getRelatedPosts(
  currentSlug: string, 
  locale: string, 
  limit: number = 3
): Promise<BlogListItem[]> {
  const currentPost = await getBlogPost(currentSlug, locale);
  if (!currentPost) return [];
  
  const allPosts = await getBlogList(locale);
  const otherPosts = allPosts.filter(post => post.slug !== currentSlug);
  
  // 简单的相关性算法：相同分类和标签的文章
  const relatedPosts = otherPosts
    .map(post => {
      let score = 0;
      
      // 相同分类加分
      if (post.category === currentPost.frontmatter.category) {
        score += 3;
      }
      
      // 相同标签加分
      const commonTags = post.tags.filter(tag => 
        currentPost.frontmatter.tags.includes(tag)
      );
      score += commonTags.length;
      
      return { ...post, score };
    })
    .filter(post => post.score > 0)
    .sort((a, b) => b.score - a.score)
    .slice(0, limit);
  
  return relatedPosts;
}
