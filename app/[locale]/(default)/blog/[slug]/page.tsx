import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { getBlogPost, getAllBlogSlugs, getRelatedPosts } from '@/lib/blog';
import { BlogLayout } from '@/components/blog/blog-layout';
import { B<PERSON><PERSON>eader } from '@/components/blog/blog-header';
import { BlogCard } from '@/components/blog/blog-card';
import { Separator } from '@/components/ui/separator';

interface BlogPostPageProps {
  params: Promise<{ locale: string; slug: string }>;
}

export async function generateStaticParams() {
  const slugs = await getAllBlogSlugs();
  return slugs.map(({ locale, slug }) => ({
    locale,
    slug,
  }));
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string; slug: string }>;
}): Promise<Metadata> {
  const { locale, slug } = await params;
  const post = await getBlogPost(slug, locale);

  if (!post) {
    return {
      title: 'Post Not Found',
    };
  }

  const { frontmatter } = post;
  
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/blog/${slug}`;
  if (locale !== 'en') {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/blog/${slug}`;
  }

  return {
    title: frontmatter.title,
    description: frontmatter.description,
    keywords: frontmatter.seo?.keywords,
    authors: [{ name: frontmatter.author }],
    alternates: {
      canonical: canonicalUrl,
    },
    openGraph: {
      title: frontmatter.title,
      description: frontmatter.seo?.ogDescription || frontmatter.description,
      type: 'article',
      url: canonicalUrl,
      images: frontmatter.seo?.ogImage || frontmatter.coverImage ? [
        {
          url: frontmatter.seo?.ogImage || frontmatter.coverImage!,
          width: 1200,
          height: 630,
          alt: frontmatter.title,
        },
      ] : undefined,
      publishedTime: frontmatter.date,
      authors: [frontmatter.author],
      tags: frontmatter.tags,
    },
    twitter: {
      card: 'summary_large_image',
      title: frontmatter.title,
      description: frontmatter.seo?.ogDescription || frontmatter.description,
      images: frontmatter.seo?.ogImage || frontmatter.coverImage ? [
        frontmatter.seo?.ogImage || frontmatter.coverImage!
      ] : undefined,
    },
  };
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { locale, slug } = await params;
  const post = await getBlogPost(slug, locale);

  if (!post) {
    notFound();
  }

  // 获取相关文章
  const relatedPosts = await getRelatedPosts(slug, locale, 3);

  const backUrl = locale === 'en' ? '/blog' : `/${locale}/blog`;

  return (
    <BlogLayout
      template={post.frontmatter.template}
      style={post.frontmatter.style}
    >
      {/* 博客头部 */}
      <BlogHeader
        frontmatter={post.frontmatter}
        locale={locale}
        readingTime={post.readingTime}
        backUrl={backUrl}
      />

      {/* 主要内容 */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="prose prose-gray dark:prose-invert max-w-none prose-headings:text-foreground prose-p:text-foreground prose-li:text-foreground prose-strong:text-foreground prose-em:text-foreground dark:prose-headings:text-gray-100 dark:prose-p:text-gray-300 dark:prose-li:text-gray-300 dark:prose-strong:text-gray-100 dark:prose-em:text-gray-200">
          <div dangerouslySetInnerHTML={{ __html: post.content }} />
        </div>
      </div>

      {/* 相关文章 */}
      {relatedPosts.length > 0 && (
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <Separator className="mb-8" />
          <section>
            <h2 className="text-2xl font-bold mb-6">
              {locale === 'zh' ? '相关文章' : 'Related Posts'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {relatedPosts.map((relatedPost) => (
                <BlogCard
                  key={relatedPost.slug}
                  post={relatedPost}
                  locale={locale}
                  variant="compact"
                  showExcerpt={false}
                />
              ))}
            </div>
          </section>
        </div>
      )}

      {/* JSON-LD 结构化数据 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'BlogPosting',
            headline: post.frontmatter.title,
            description: post.frontmatter.description,
            image: post.frontmatter.coverImage,
            author: {
              '@type': 'Person',
              name: post.frontmatter.author,
            },
            publisher: {
              '@type': 'Organization',
              name: 'AI ASMR Generator',
              logo: {
                '@type': 'ImageObject',
                url: `${process.env.NEXT_PUBLIC_WEB_URL}/logo.png`,
              },
            },
            datePublished: post.frontmatter.date,
            dateModified: post.frontmatter.date,
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': `${process.env.NEXT_PUBLIC_WEB_URL}${locale === 'en' ? '' : `/${locale}`}/blog/${slug}`,
            },
            keywords: post.frontmatter.tags.join(', '),
            articleSection: post.frontmatter.category,
          }),
        }}
      />
    </BlogLayout>
  );
}
